// types/package.ts

// Core Package Types
export interface PackageEntry {
  id: number;
  name: string;
  destination: string;
  region: string;
  activity: string;
  slug: string;
  published: boolean;
}

// Itinerary Types
export interface ItineraryItem {
  id: number;
  day: string;
  title: string;
  details: string;
  image?: string;
  imageFile?: File;
  heading: string;
  trekDistance: string;
  flightHours: string;
  drivingHour: string;
  highestAltitude: string;
  trekDuration: string;
}

// SEO Types
export interface SeoFields {
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
  canonicalUrl: string;
}

// Content Section Types
export interface PhotoEntry {
  file: File | null;
  caption: string;
}

export interface InfoEntry {
  title: string;
  body: string;
  note: string;
}

// Package Form Data Types
export interface PackageFormData {
  // Basic Details
  packageName: string;
  region: string;
  accommodation: string;
  trailType: string;
  maxAltitude: string;
  groupSize: string;
  bestSeason: string;
  price: string;
  activityPerDay: string;
  grade: string;
  activity: string;
  slug: string;
  distance: string;
  daysNights: string;
  meals: string;
  discountPrice: string;
  transportation: string;
  imageAlt: string;
  bookingLink: string;
  overview: string;
  
  // File uploads
  mainImage?: File;
  thumbnail?: File;
  pdfFile?: File;
}

// Package Status Controls
export interface PackageStatusControls {
  published: boolean;
  tripOfTheMonth: boolean;
  popularTours: boolean;
  shortTrek: boolean;
}

// Content Sections Data
export interface PackageContentData {
  // Highlights Section
  highlightsTitle: string;
  highlightsBody: string;
  
  // Description Section
  descriptionTitle: string;
  descriptionBody: string;
  
  // Short Itinerary Section
  shortItineraryTitle: string;
  shortItineraryItems: string[];
  
  // Photo Section
  photoTitle: string;
  photos: PhotoEntry[];
  
  // Video Section
  videoTitle: string;
  youtubeLinks: string[];
  
  // Includes Section
  includesTitle: string;
  includesBody: string;
  
  // Excludes Section
  excludesTitle: string;
  excludesBody: string;
  
  // Map Section
  mapTitle: string;
  mapFile?: File;
  
  // Trip Info Section
  tripInfoTitle: string;
  tripInfos: InfoEntry[];
}

// Complete Package Data Structure
export interface PackageData {
  id?: number;
  formData: PackageFormData;
  statusControls: PackageStatusControls;
  contentData: PackageContentData;
  itineraryItems: ItineraryItem[];
  seo: SeoFields;
  schema: string;
}

// Component Props Types
export interface CollapsibleSectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

export interface AddItineraryFormProps {
  editingItem: ItineraryItem | null;
  onAddItinerary: (data: Omit<ItineraryItem, 'id'>) => void;
  onUpdateItinerary: (data: ItineraryItem) => void;
  onCancelEdit: () => void;
}

export interface ItineraryListProps {
  items: ItineraryItem[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
}

export interface PackageSidebarProps {
  activeHighlight: string;
  onHighlightChange: (highlight: string) => void;
}

export interface TripHighlightsContentProps {
  activeHighlight: string;
  packageData?: PackageContentData;
  onContentChange?: (field: keyof PackageContentData, value: any) => void;
}

// Package Page Props
export interface PackagePageProps {
  mode: 'create' | 'edit';
  packageId?: number;
  initialData?: PackageData;
  onSave?: (data: PackageData) => void;
  onCancel?: () => void;
}

// API Response Types
export interface PackageApiResponse {
  success: boolean;
  data?: PackageData;
  message?: string;
}

export interface PackageListApiResponse {
  success: boolean;
  data?: PackageEntry[];
  message?: string;
}

// Form Validation Types
export interface PackageFormErrors {
  packageName?: string;
  region?: string;
  activity?: string;
  slug?: string;
  price?: string;
  // Add other validation fields as needed
}

// File Upload Types
export interface FileUploadProps {
  label: string;
  accept: string;
  showPreview?: boolean;
  previewSrc?: string;
  previewAlt?: string;
  onFileChange: (file: File | null) => void;
}

// Tab Types
export type PackageTabType = 'details' | 'itinerary' | 'equipment' | 'cost' | 'discount' | 'faq';

// Highlight Section Types
export type HighlightSectionType = 
  | 'highlights' 
  | 'description' 
  | 'shortItinerary' 
  | 'photo' 
  | 'video' 
  | 'includes' 
  | 'excludes' 
  | 'map' 
  | 'tripInfo';



// // /types/index.ts

// // For simple content sections with a title and rich text body
// export interface ContentSection {
//   title: string;
//   body: string;
// }

// // For individual photos in the gallery
// export interface PhotoEntry {
//   id: number;
//   file: File | null;
//   previewUrl?: string; // To show existing images when editing
//   caption: string;
// }

// // For individual "trip info" accordions
// export interface InfoEntry {
//   id: number;
//   title: string;
//   body: string;
//   note: string;
// }

// // For a single day in the itinerary
// export interface ItineraryItem {
//   id: number;
//   day: string;
//   title: string;
//   details: string;
//   image?: string;
//   imageFile?: File;
//   heading: string;
//   trekDistance: string;
//   flightHours: string;
//   drivingHour: string;
//   highestAltitude: string;
//   trekDuration: string;
// }

// // For SEO metadata
// export interface SeoFields {
//   metaTitle: string;
//   metaDescription: string;
//   metaKeywords: string;
//   canonicalUrl:string;
// }

// // The Master Package Type
// export interface Package {
//   id: number | null;
//   name: string;
//   slug: string;
//   region: string;
//   activity: string;
//   accommodation: string;
//   trailType: string;
//   maxAltitude: string;
//   groupSize: string;
//   bestSeason: string;
//   price: string;
//   discountPrice: string;
//   activityPerDay: string;
//   grade: string;
//   imageAlt: string;
//   bookingLink: string;
//   distance: string;
//   daysNights: string;
//   meals: string;
//   transportation: string;
//   published: boolean;
//   overview: string;

//   // Files (handled separately)
//   imageFile?: File;
//   pdfFile?: File;
//   thumbnailFile?: File;

//   // Main Image URLs for display
//   imageUrl?: string;
//   thumbnailUrl?: string;

//   // Complex Content Sections
//   description: ContentSection;
//   includes: ContentSection;
//   excludes: ContentSection;
//   tripHighlights: ContentSection;
//   shortItinerary: ContentSection;

//   // Dynamic/Repeating Content Sections
//   photos: {
//     title: string;
//     items: PhotoEntry[];
//   };
//   videos: {
//     title: string;
//     links: { id: number; url: string }[];
//   };
//   tripInfos: {
//     title: string;
//     items: InfoEntry[];
//   };

//   // Itinerary & SEO
//   itinerary: ItineraryItem[];
//   seo: SeoFields;
//   schema: string;
// }


// // export interface PackageFormData {
// //   packageName: string
// //   slug: string
// //   region: string
// //   destination: string
// //   activity: string
// //   accommodation: string
// //   maxAltitude: string
// //   groupSize: string
// //   bestSeason: string
// //   price: string
// //   discountPrice: string
// //   activityPerDay: string
// //   grade: string
// //   image: File | null
// //   packagePdf: File | null
// //   thumbnailImage: File | null
// //   bookingLink: string
// //   description: string
// //   distance: string
// //   daysAndNights: string
// //   mealsInclude: string
// //   natureOfTrek: string
// //   tripCode: string
// //   transportation: string
// //   imageAltTag: string
// //   published: boolean
// //   tripOfTheMonth: boolean
// //   popularTours: boolean
// //   shortTrek: boolean
// // }

// // export interface SeoFields {
// //   metaTitle: string
// //   metaDescription: string
// //   metaKeywords: string
// //   canonicalUrl: string
// // }
